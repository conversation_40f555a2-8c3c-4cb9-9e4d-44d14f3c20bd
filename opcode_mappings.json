{"description": "LuaJIT 混淆指令码映射配置文件", "version": "1.0", "mappings": {"shuffled": {"description": "从运行时提取的混淆指令码映射表", "source": "libcocos2dlua.so 运行时分析", "mapping": {"0": "BC_ISLT", "1": "BC_ISGE", "2": "BC_ISLE", "3": "BC_ISGT", "4": "BC_ISEQV", "5": "BC_ISNEV", "6": "BC_ISEQS", "7": "BC_ISNES", "8": "BC_ISEQN", "9": "BC_ISNEN", "10": "BC_ISEQP", "11": "BC_ISNEP", "12": "BC_ISFC", "13": "BC_ISTC", "14": "BC_ISF", "15": "BC_IST", "16": "BC_ISTYPE", "17": "BC_ISNUM", "18": "BC_NOT", "19": "BC_MOV", "20": "BC_LEN", "21": "BC_UNM", "22": "BC_ADDVN", "23": "BC_SUBVN", "24": "BC_MULVN", "25": "BC_DIVVN", "26": "BC_MODVN", "27": "BC_ADDNV", "28": "BC_SUBNV", "29": "BC_MULNV", "30": "BC_DIVNV", "31": "BC_MODNV", "32": "BC_ADDVV", "33": "BC_SUBVV", "34": "BC_MULVV", "35": "BC_DIVVV", "36": "BC_MODVV", "37": "BC_POW", "38": "BC_CAT", "39": "BC_UGET", "40": "BC_USETV", "41": "BC_USETS", "42": "BC_USETN", "43": "BC_USETP", "44": "BC_UCLO", "45": "BC_FNEW", "46": "BC_KSTR", "47": "BC_KCDATA", "48": "BC_KSHORT", "49": "BC_KNUM", "50": "BC_KPRI", "51": "BC_KNIL", "52": "BC_TNEW", "53": "BC_TDUP", "54": "BC_GGET", "55": "BC_GSET", "56": "BC_TGETV", "57": "BC_TGETS", "58": "BC_TGETB", "59": "BC_TGETR", "60": "BC_TSETV", "61": "BC_TSETS", "62": "BC_TSETB", "63": "BC_TSETM", "64": "BC_TSETR", "65": "BC_CALLM", "66": "BC_CALL", "67": "BC_CALLMT", "68": "BC_CALLT", "69": "BC_ITERC", "70": "BC_ITERN", "71": "BC_INVALID", "72": "BC_VARG", "73": "BC_ISNEXT", "74": "BC_RETM", "75": "BC_RET", "76": "BC_RET0", "77": "BC_RET1", "78": "BC_FORI", "79": "BC_JFORL", "80": "BC_FORL", "81": "BC_IFORL", "82": "BC_JFORL", "83": "BC_ITERL", "84": "BC_IITERL", "85": "BC_JITERL", "86": "BC_LOOP", "87": "BC_ILOOP", "88": "BC_JLOOP", "89": "BC_JMP", "90": "BC_FUNCF", "91": "BC_IFUNCF", "92": "BC_JFUNCF", "93": "BC_FUNCV", "94": "BC_IFUNCV", "95": "BC_JFUNCV", "96": "BC_FUNCC", "97": "BC_FUNCCW"}, "notes": ["注意：Index 12 和 13 的映射与标准LuaJIT相比是交换的", "Index 71 缺失，映射为 BC_INVALID", "Index 79 和 82 都映射到 BC_JFORL（可能是重复）", "这个映射表是从运行时分析中提取的，适用于特定的混淆版本"]}}, "detection_patterns": {"file_patterns": ["*modded*", "*obfuscated*", "*shuffled*", "*encrypted*"], "heuristics": {"unusual_opcode_distribution": true, "invalid_standard_opcodes": true, "high_frequency_rare_opcodes": true}}}