// LuaJIT Opcode提取脚本
// 用于从libcocos2dlua.so中提取实际的opcode处理例程地址

var output = false;
var lib_base = null;
var hookInstalled = false;

const moduleName = "libcocos2dlua.so";

function findModule() {
    try {
        if (typeof Module !== 'undefined' && Module.findBaseAddress) {
            lib_base = Module.findBaseAddress(moduleName);
        }
        if (!lib_base) {
            Process.enumerateModules().forEach(function(module) {
                if (module.name === moduleName) {
                    lib_base = module.base;
                    console.log("[*] 找到模块: " + moduleName + " at " + lib_base);
                }
            });
        }
        return lib_base !== null;
    } catch (e) {
        console.log("[!] 查找模块错误: " + e.message);
        return false;
    }
}

// 延迟查找模块，给游戏时间加载
console.log("[*] 等待4秒让游戏加载模块...");
setTimeout(function() {
    console.log("[*] 开始查找模块...");

    var retryCount = 0;
    var maxRetries = 5;

    function tryFindModule() {
        retryCount++;
        console.log("[*] 尝试 " + retryCount + "/" + maxRetries + " 查找模块...");

        if (findModule()) {
            console.log("[*] 成功找到模块: " + lib_base);
            installHook();
        } else {
            if (retryCount < maxRetries) {
                console.log("[*] 模块未找到，2秒后重试...");
                setTimeout(tryFindModule, 2000);
            } else {
                console.log("[!] 模块 " + moduleName + " 未找到，列出所有已加载的模块:");
                Process.enumerateModules().forEach(function(module) {
                    console.log("  " + module.name + " at " + module.base);
                });
            }
        }
    }

    tryFindModule();
}, 4000);

// 扩展的LuaJIT opcode名称表（包含魔改版可能的扩展）
var opcode_names = [
    "ISLT", "ISGE", "ISLE", "ISGT", "ISEQV", "ISNEV", "ISEQS", "ISNES",
    "ISEQN", "ISNEN", "ISEQP", "ISNEP", "ISTC", "ISFC", "IST", "ISF",
    "ISTYPE", "ISNUM", "MOV", "NOT", "UNM", "LEN", "ADDVN", "SUBVN",
    "MULVN", "DIVVN", "MODVN", "ADDNV", "SUBNV", "MULNV", "DIVNV", "MODNV",
    "ADDVV", "SUBVV", "MULVV", "DIVVV", "MODVV", "POW", "CAT", "KSTR",
    "KCDATA", "KSHORT", "KNUM", "KPRI", "KNIL", "UGET", "USETV", "USETS",
    "USETN", "USETP", "UCLO", "FNEW", "TNEW", "TDUP", "GGET", "GSET",
    "TGETV", "TGETS", "TGETB", "TGETR", "TSETV", "TSETS", "TSETB", "TSETM",
    "TSETR", "CALLM", "CALL", "CALLMT", "CALLT", "ITERC", "ITERN", "VARG",
    "ISNEXT", "RETM", "RET", "RET0", "RET1", "FORI", "JFORI", "FORL",
    "IFORL", "JFORL", "ITERL", "IITERL", "JITERL", "LOOP", "ILOOP", "JLOOP",
    "JMP", "FUNCF", "IFUNCF", "JFUNCF", "FUNCV", "IFUNCV", "JFUNCV", "FUNCC",
    "FUNCCW",
    // 魔改版可能的扩展opcode
    "EXT_98", "EXT_99", "EXT_100", "EXT_101", "EXT_102", "EXT_103", "EXT_104",
    "EXT_105", "EXT_106", "EXT_107", "EXT_108", "EXT_109", "EXT_110", "EXT_111",
    "EXT_112", "EXT_113", "EXT_114", "EXT_115", "EXT_116", "EXT_117", "EXT_118",
    "EXT_119", "EXT_120", "EXT_121", "EXT_122", "EXT_123", "EXT_124", "EXT_125",
    "EXT_126", "EXT_127", "EXT_128", "EXT_129", "EXT_130", "EXT_131", "EXT_132",
    "EXT_133", "EXT_134", "EXT_135", "EXT_136", "EXT_137", "EXT_138", "EXT_139",
    "EXT_140", "EXT_141", "EXT_142", "EXT_143", "EXT_144", "EXT_145", "EXT_146",
    "EXT_147", "EXT_148", "EXT_149", "EXT_150", "EXT_151", "EXT_152", "EXT_153",
    "EXT_154", "EXT_155", "EXT_156", "EXT_157", "EXT_158", "EXT_159", "EXT_160"
];

// Hook安装函数
function installHook() {
    if (hookInstalled) {
        console.log("[*] Hook已经安装，跳过");
        return;
    }

    const offset = 0xBEDFD4; // 原始偏移
    const targetAddr = lib_base.add(offset);
    console.log("[*] 尝试Hook地址: " + targetAddr);

    try {
        Interceptor.attach(targetAddr, {
            onEnter: function() {
                if (!output) {
                    console.log("[+] Hook triggered, extracting opcodes...");

                    try {
                        var GL = this.context.x22;
                        if (!GL) {
                            console.log("[!] 无法获取GL寄存器，尝试其他寄存器...");
                            // 尝试其他可能的寄存器
                            GL = this.context.x21 || this.context.x20 || this.context.x19;
                        }

                        if (!GL) {
                            console.log("[!] 无法获取全局状态指针");
                            return;
                        }

                        var dispatch = GL.add(0xF70);

                        console.log("[+] GL address: " + GL);
                        console.log("[+] Dispatch table address: " + dispatch);
                        console.log("");
                        console.log("=== LuaJIT Opcode Analysis ===");
                        console.log("Index | Opcode Name | Handler Address | Offset from Base | Status");
                        console.log("------|-------------|-----------------|------------------|--------");

                        var opcodes_info = [];
                        var unique_addresses = new Set();
                        var duplicate_addresses = new Map();
                        var max_opcodes = 200; // 扩展到200个opcode

                        // 第一遍：收集所有地址信息
                        for (var i = 0; i < max_opcodes; ++i) {
                            try {
                                var prog_ptr = dispatch.add(i * 8).readPointer();
                                var offset = prog_ptr.sub(lib_base);
                                var opcode_name = (i < opcode_names.length) ? opcode_names[i] : "UNKNOWN_" + i;

                                var addr_str = prog_ptr.toString();
                                var status = "";

                                if (unique_addresses.has(addr_str)) {
                                    status = "DUP";
                                    if (!duplicate_addresses.has(addr_str)) {
                                        duplicate_addresses.set(addr_str, []);
                                    }
                                    duplicate_addresses.get(addr_str).push(i);
                                } else {
                                    unique_addresses.add(addr_str);
                                    status = "NEW";
                                }

                                console.log(sprintf("%5d | %11s | %15s | 0x%08x | %s",
                                    i, opcode_name, prog_ptr, offset.toInt32(), status));

                                opcodes_info.push({
                                    index: i,
                                    name: opcode_name,
                                    handler: prog_ptr,
                                    offset: offset.toInt32(),
                                    status: status
                                });
                            } catch (e) {
                                console.log("[!] 读取opcode " + i + " 失败: " + e);
                                break; // 如果读取失败，可能已经超出范围
                            }
                        }
                    } catch (e) {
                        console.log("[!] Hook执行失败: " + e);
                    }

                    console.log("");
                    console.log("=== 重复地址分析 ===");
                    console.log("以下地址被多个opcode共享（可能是fallback处理）：");

                    duplicate_addresses.forEach(function(indices, address) {
                        var first_info = opcodes_info.find(function(info) {
                            return info.handler.toString() === address;
                        });
                        if (first_info) {
                            console.log("地址 " + address + " (0x" +
                                first_info.offset.toString(16).padStart(8, '0').toUpperCase() +
                                ") 被以下opcode共享:");
                            indices.forEach(function(idx) {
                                if (idx < opcodes_info.length) {
                                    console.log("  [" + idx + "] " + opcodes_info[idx].name);
                                }
                            });
                        }
                    });

                    console.log("");
                    console.log("=== 唯一opcode统计 ===");
                    var unique_opcodes = opcodes_info.filter(function(info) {
                        return info.status === "NEW";
                    });
                    console.log("总opcode数: " + opcodes_info.length);
                    console.log("唯一实现数: " + unique_opcodes.length);
                    console.log("重复/共享数: " + (opcodes_info.length - unique_opcodes.length));

                    console.log("");
                    console.log("=== 魔改版扩展opcode ===");
                    var extended_opcodes = opcodes_info.filter(function(info) {
                        return info.index >= 97 && info.status === "NEW";
                    });

                    if (extended_opcodes.length > 0) {
                        console.log("发现 " + extended_opcodes.length + " 个魔改版扩展opcode:");
                        extended_opcodes.forEach(function(info) {
                            console.log("  [" + info.index + "] " + info.name + " -> 0x" +
                                info.offset.toString(16).padStart(8, '0').toUpperCase());
                        });
                    } else {
                        console.log("未发现魔改版扩展opcode（或都是重复地址）");
                    }

                    console.log("");
                    console.log("=== 建议的修复方案 ===");
                    console.log("1. 重点关注唯一实现的opcode");
                    console.log("2. 重复地址的opcode可能共享相同的处理逻辑");
                    console.log("3. 魔改版扩展opcode需要特别分析");
                    console.log("4. 建议先实现标准opcode，再处理扩展部分");

                    output = true;
                }
        },
        onLeave: function() {}
    });

        hookInstalled = true;
        console.log("[*] Hook安装成功！");

    } catch (e) {
        console.log("[!] Hook失败: " + e.message);
    }
}

// 辅助函数：格式化输出
function sprintf(format, index, name, address, offset, status) {
    var result = format.replace(/%5d/g, index.toString().padStart(5, ' '))
                      .replace(/%11s/g, name.toString().padEnd(11, ' '))
                      .replace(/%15s/g, address.toString())
                      .replace(/0x%08x/g, '0x' + offset.toString(16).padStart(8, '0').toUpperCase());

    // 如果有status参数，添加到末尾
    if (status !== undefined) {
        result = result.replace(/%s/g, status.padEnd(6, ' '));
    }

    return result;
}

console.log("[+] LuaJIT Opcode提取脚本已加载");
console.log("[+] 将在4秒后开始查找模块...");
console.log("[+] 请确保游戏已完全启动并加载了Lua模块");
