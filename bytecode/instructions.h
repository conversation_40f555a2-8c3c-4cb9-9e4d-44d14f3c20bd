static constexpr uint16_t BC_OP_JMP_BIAS = 0x8000;

enum BC_OP {
	BC_OP_ISLT, // if A<VAR> < D<VAR> then JMP
	BC_OP_ISGE, // if not (A<VAR> < D<VAR>) then JMP
	BC_OP_ISLE, // if A<VAR> <= D<VAR> then JMP
	BC_OP_ISGT, // if not (A<VAR> <= D<VAR>) then JMP
	BC_OP_ISEQV, // if A<VAR> == D<VAR> then JMP
	BC_OP_ISNEV, // if A<VAR> ~= D<VAR> then JMP
	BC_OP_ISEQS, // if A<VAR> == D<STR> then JMP
	BC_OP_ISNES, // if A<VAR> ~= D<STR> then JMP
	BC_OP_ISEQN, // if A<VAR> == D<NUM> then JMP
	BC_OP_ISNEN, // if A<VAR> ~= D<NUM> then JMP
	BC_OP_ISEQP, // if A<VAR> == D<PRI> then JMP
	BC_OP_ISNEP, // if A<VAR> ~= D<PRI> then JMP
	BC_OP_ISTC, // if D<VAR> then A<DST> = D and JMP
	BC_OP_ISFC, // if not D<VAR> then A<DST> = D and JMP
	BC_OP_IST, // if D<VAR> then JMP
	BC_OP_ISF, // if not D<VAR> then JMP
	BC_OP_ISTYPE, // unsupported
	BC_OP_ISNUM, // unsupported
	BC_OP_MOV, // A<DST> = D<VAR>
	BC_OP_NOT, // A<DST> = not D<VAR>
	BC_OP_UNM, // A<DST> = -D<VAR>
	BC_OP_LEN, // A<DST> = #D<VAR>
	BC_OP_ADDVN, // A<DST> = B<VAR> + C<NUM>
	BC_OP_SUBVN, // A<DST> = B<VAR> - C<NUM>
	BC_OP_MULVN, // A<DST> = B<VAR> * C<NUM>
	BC_OP_DIVVN, // A<DST> = B<VAR> / C<NUM>
	BC_OP_MODVN, // A<DST> = B<VAR> % C<NUM>
	BC_OP_ADDNV, // A<DST> = C<NUM> + B<VAR>
	BC_OP_SUBNV, // A<DST> = C<NUM> - B<VAR>
	BC_OP_MULNV, // A<DST> = C<NUM> * B<VAR>
	BC_OP_DIVNV, // A<DST> = C<NUM> / B<VAR>
	BC_OP_MODNV, // A<DST> = C<NUM> % B<VAR>
	BC_OP_ADDVV, // A<DST> = B<VAR> + C<VAR>
	BC_OP_SUBVV, // A<DST> = B<VAR> - C<VAR>
	BC_OP_MULVV, // A<DST> = B<VAR> * C<VAR>
	BC_OP_DIVVV, // A<DST> = B<VAR> / C<VAR>
	BC_OP_MODVV, // A<DST> = B<VAR> % C<VAR>
	BC_OP_POW, // A<DST> = B<VAR> ^ C<VAR>
	BC_OP_CAT, // A<DST> = B<RBASE> .. B++ -> C<RBASE>
	BC_OP_KSTR, // A<DST> = D<STR>
	BC_OP_KCDATA, // A<DST> = D<CDATA>
	BC_OP_KSHORT, // A<DST> = D<LITS>
	BC_OP_KNUM, // A<DST> = D<NUM>
	BC_OP_KPRI, // A<DST> = D<PRI>
	BC_OP_KNIL, // A<BASE>, A++ -> D<BASE> = nil
	BC_OP_UGET, // A<DST> = D<UV>
	BC_OP_USETV, // A<UV> = D<VAR>
	BC_OP_USETS, // A<UV> = D<STR>
	BC_OP_USETN, // A<UV> = D<NUM>
	BC_OP_USETP, // A<UV> = D<PRI>
	BC_OP_UCLO, // upvalue close for A<RBASE>, A++ -> framesize; goto D<JUMP>
	BC_OP_FNEW, // A<DST> = D<FUNC>
	BC_OP_TNEW, // A<DST> = {}
	BC_OP_TDUP, // A<DST> = D<TAB>
	BC_OP_GGET, // A<DST> = _G.D<STR>
	BC_OP_GSET, // _G.D<STR> = A<VAR>
	BC_OP_TGETV, // A<DST> = B<VAR>[C<VAR>]
	BC_OP_TGETS, // A<DST> = B<VAR>[C<STR>]
	BC_OP_TGETB, // A<DST> = B<VAR>[C<LIT>]
	BC_OP_TGETR, // unsupported
	BC_OP_TSETV, // B<VAR>[C<VAR>] = A<VAR>
	BC_OP_TSETS, // B<VAR>[C<STR>] = A<VAR>
	BC_OP_TSETB, // B<VAR>[C<LIT>] = A<VAR>
	BC_OP_TSETM, // A-1<BASE>[D&0xFFFFFFFF<NUM>] <- A (<- multres)
	BC_OP_TSETR, // unsupported
	BC_OP_CALLM, // if B<LIT> == 0 then A<BASE> (<- multres) <- A(A+FR2?2:1, A++ -> for C<LIT>, A++ (<- multres)) else A, A++ -> for B-1 = A(A+FR2?2:1, A++ -> for C, A++ (<- multres))
	BC_OP_CALL, // if B<LIT> == 0 then A<BASE> (<- multres) <- A(A+FR2?2:1, A++ -> for C-1<LIT>) else A, A++ -> for B-1 = A(A+FR2?2:1, A++ -> for C-1)
	BC_OP_CALLMT, // return A<BASE>(A+FR2?2:1, A++ -> for D<LIT>, A++ (<- multres))
	BC_OP_CALLT, // return A<BASE>(A+FR2?2:1, A++ -> for D-1<LIT>)
	BC_OP_ITERC, // for A<BASE>, A++ -> for B-1<LIT> in A-3, A-2, A-1 do
	BC_OP_ITERN, // for A<BASE>, A++ -> for B-1<LIT> in A-3, A-2, A-1 do
	BC_OP_VARG, // if B<LIT> == 0 then A<BASE> (<- multres) <- ... else A, A++ -> for B-1 = ...
	BC_OP_ISNEXT, // goto ITERN at D<JUMP>
	BC_OP_RETM, // return A<BASE>, A++ -> for D<LIT>, A++ (<- multres)
	BC_OP_RET, // return A<RBASE>, A++ -> for D-1<LIT>
	BC_OP_RET0, // return
	BC_OP_RET1, // return A<RBASE>
	BC_OP_FORI, // for A+3<BASE> = A, A+1, A+2 do; exit at D<JUMP>
	BC_OP_JFORI, // unsupported
	BC_OP_FORL, // end of numeric for loop; start at D<JUMP>
	BC_OP_IFORL, // unsupported
	BC_OP_JFORL, // unsupported
	BC_OP_ITERL, // end of generic for loop; start at D<JUMP>
	BC_OP_IITERL, // unsupported
	BC_OP_JITERL, // unsupported
	BC_OP_LOOP, // if D<JUMP> == 32767 then goto loop else while/repeat loop; exit at D
	BC_OP_ILOOP, // unsupported
	BC_OP_JLOOP, // unsupported
	BC_OP_JMP, // goto D<JUMP> or if true then JMP or goto ITERC at D
	BC_OP_FUNCF, // unsupported
	BC_OP_IFUNCF, // unsupported
	BC_OP_JFUNCF, // unsupported
	BC_OP_FUNCV, // unsupported
	BC_OP_IFUNCV, // unsupported
	BC_OP_JFUNCV, // unsupported
	BC_OP_FUNCC, // unsupported
	BC_OP_FUNCCW, // unsupported
	BC_OP_INVALID
};

// 动态指令码映射类
class OpcodeMapper {
public:
    // 映射模式枚举
    enum MappingMode {
        STANDARD,    // 标准LuaJIT映射
        SHUFFLED     // 混淆/乱序映射
    };

private:
    MappingMode currentMode;

    // 混淆映射表：从混淆的字节码索引到标准BC_OP的映射
    static constexpr BC_OP shuffledMapping[98] = {
        BC_OP_ISLT,     // Index 0
        BC_OP_ISGE,     // Index 1
        BC_OP_ISLE,     // Index 2
        BC_OP_ISGT,     // Index 3
        BC_OP_ISEQV,    // Index 4
        BC_OP_ISNEV,    // Index 5
        BC_OP_ISEQS,    // Index 6
        BC_OP_ISNES,    // Index 7
        BC_OP_ISEQN,    // Index 8
        BC_OP_ISNEN,    // Index 9
        BC_OP_ISEQP,    // Index 10
        BC_OP_ISNEP,    // Index 11
        BC_OP_ISFC,     // Index 12 (注意：原来是13，现在是12)
        BC_OP_ISTC,     // Index 13 (注意：原来是12，现在是13)
        BC_OP_ISF,      // Index 14
        BC_OP_IST,      // Index 15
        BC_OP_ISTYPE,   // Index 16
        BC_OP_ISNUM,    // Index 17
        BC_OP_NOT,      // Index 18
        BC_OP_MOV,      // Index 19
        BC_OP_LEN,      // Index 20
        BC_OP_UNM,      // Index 21
        BC_OP_ADDVN,    // Index 22
        BC_OP_SUBVN,    // Index 23
        BC_OP_MULVN,    // Index 24
        BC_OP_DIVVN,    // Index 25
        BC_OP_MODVN,    // Index 26
        BC_OP_ADDNV,    // Index 27
        BC_OP_SUBNV,    // Index 28
        BC_OP_MULNV,    // Index 29
        BC_OP_DIVNV,    // Index 30
        BC_OP_MODNV,    // Index 31
        BC_OP_ADDVV,    // Index 32
        BC_OP_SUBVV,    // Index 33
        BC_OP_MULVV,    // Index 34
        BC_OP_DIVVV,    // Index 35
        BC_OP_MODVV,    // Index 36
        BC_OP_POW,      // Index 37
        BC_OP_CAT,      // Index 38
        BC_OP_UGET,     // Index 39
        BC_OP_USETV,    // Index 40
        BC_OP_USETS,    // Index 41
        BC_OP_USETN,    // Index 42
        BC_OP_USETP,    // Index 43
        BC_OP_UCLO,     // Index 44
        BC_OP_FNEW,     // Index 45
        BC_OP_KSTR,     // Index 46
        BC_OP_KCDATA,   // Index 47
        BC_OP_KSHORT,   // Index 48
        BC_OP_KNUM,     // Index 49
        BC_OP_KPRI,     // Index 50
        BC_OP_KNIL,     // Index 51
        BC_OP_TNEW,     // Index 52
        BC_OP_TDUP,     // Index 53
        BC_OP_GGET,     // Index 54
        BC_OP_GSET,     // Index 55
        BC_OP_TGETV,    // Index 56
        BC_OP_TGETS,    // Index 57
        BC_OP_TGETB,    // Index 58
        BC_OP_TGETR,    // Index 59
        BC_OP_TSETV,    // Index 60
        BC_OP_TSETS,    // Index 61
        BC_OP_TSETB,    // Index 62
        BC_OP_TSETM,    // Index 63
        BC_OP_TSETR,    // Index 64
        BC_OP_CALLM,    // Index 65
        BC_OP_CALL,     // Index 66
        BC_OP_CALLMT,   // Index 67
        BC_OP_CALLT,    // Index 68
        BC_OP_ITERC,    // Index 69
        BC_OP_ITERN,    // Index 70
        BC_OP_INVALID,  // Index 71 (缺失)
        BC_OP_VARG,     // Index 72
        BC_OP_ISNEXT,   // Index 73
        BC_OP_RETM,     // Index 74
        BC_OP_RET,      // Index 75
        BC_OP_RET0,     // Index 76
        BC_OP_RET1,     // Index 77
        BC_OP_FORI,     // Index 78
        BC_OP_JFORL,    // Index 79 (注意：这里是JFORL)
        BC_OP_FORL,     // Index 80
        BC_OP_IFORL,    // Index 81
        BC_OP_JFORL,    // Index 82 (重复)
        BC_OP_ITERL,    // Index 83
        BC_OP_IITERL,   // Index 84
        BC_OP_JITERL,   // Index 85
        BC_OP_LOOP,     // Index 86
        BC_OP_ILOOP,    // Index 87
        BC_OP_JLOOP,    // Index 88
        BC_OP_JMP,      // Index 89
        BC_OP_FUNCF,    // Index 90
        BC_OP_IFUNCF,   // Index 91
        BC_OP_JFUNCF,   // Index 92
        BC_OP_FUNCV,    // Index 93
        BC_OP_IFUNCV,   // Index 94
        BC_OP_JFUNCV,   // Index 95
        BC_OP_FUNCC,    // Index 96
        BC_OP_FUNCCW    // Index 97
    };

public:
    OpcodeMapper() : currentMode(STANDARD) {}

    // 设置映射模式
    void setMappingMode(MappingMode mode) {
        currentMode = mode;
    }

    // 获取当前映射模式
    MappingMode getMappingMode() const {
        return currentMode;
    }

    // 将字节码索引映射到标准BC_OP
    BC_OP mapOpcode(uint8_t opcodeIndex, uint8_t version) const {
        if (currentMode == SHUFFLED) {
            // 使用混淆映射表
            if (opcodeIndex < sizeof(shuffledMapping) / sizeof(shuffledMapping[0])) {
                return shuffledMapping[opcodeIndex];
            }
            return BC_OP_INVALID;
        } else {
            // 使用原有的标准映射逻辑
            return (BC_OP)(version == Bytecode::BC_VERSION_1 && opcodeIndex >= BC_OP_ISTYPE ?
                          (opcodeIndex >= BC_OP_TGETR - 2 ?
                           (opcodeIndex >= BC_OP_TSETR - 3 ? opcodeIndex + 4 : opcodeIndex + 3) :
                           opcodeIndex + 2) : opcodeIndex);
        }
    }

    // 检测是否为混淆的字节码（启发式检测）
    static bool detectShuffledBytecode(const std::vector<uint8_t>& bytecode) {
        // 简单的启发式检测：检查指令分布是否异常
        std::vector<int> opcodeFreq(98, 0);

        for (size_t i = 0; i < bytecode.size(); i += 4) { // 假设每条指令4字节
            if (i < bytecode.size()) {
                uint8_t opcode = bytecode[i];
                if (opcode < 98) {
                    opcodeFreq[opcode]++;
                }
            }
        }

        // 检查是否有异常的指令分布模式
        // 这里可以添加更复杂的检测逻辑
        return false; // 暂时返回false，需要更多样本来完善检测算法
    }
};

};

// 全局映射器实例声明
extern OpcodeMapper g_opcodeMapper;

struct Instruction {
	BC_OP type;
	uint8_t a = 0;
	uint8_t b = 0;
	uint8_t c = 0;
	uint16_t d = 0;
};

static BC_OP get_op_type(const uint8_t& byte, const uint8_t& version) {
	return g_opcodeMapper.mapOpcode(byte, version);
}

static bool is_op_abc_format(const BC_OP& instruction) {
	switch (instruction) {
	case BC_OP_ADDVN:
	case BC_OP_SUBVN:
	case BC_OP_MULVN:
	case BC_OP_DIVVN:
	case BC_OP_MODVN:
	case BC_OP_ADDNV:
	case BC_OP_SUBNV:
	case BC_OP_MULNV:
	case BC_OP_DIVNV:
	case BC_OP_MODNV:
	case BC_OP_ADDVV:
	case BC_OP_SUBVV:
	case BC_OP_MULVV:
	case BC_OP_DIVVV:
	case BC_OP_MODVV:
	case BC_OP_POW:
	case BC_OP_CAT:
	case BC_OP_TGETV:
	case BC_OP_TGETS:
	case BC_OP_TGETB:
	case BC_OP_TGETR:
	case BC_OP_TSETV:
	case BC_OP_TSETS:
	case BC_OP_TSETB:
	case BC_OP_TSETR:
	case BC_OP_CALLM:
	case BC_OP_CALL:
	case BC_OP_ITERC:
	case BC_OP_ITERN:
	case BC_OP_VARG:
		return true;
	}

	return false;
}
