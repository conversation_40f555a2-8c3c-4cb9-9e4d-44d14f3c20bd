static constexpr uint16_t BC_OP_JMP_BIAS = 0x8000;

// 魔改LuaJIT的混淆映射表：从混淆的字节码索引到标准BC_OP的映射
// 根据您提供的运行时分析结果，总共97个指令
static constexpr int SHUFFLED_MAPPING[97] = {
    0,  // Index 0 -> BC_ISLT
    1,  // Index 1 -> BC_ISGE
    2,  // Index 2 -> BC_ISLE
    3,  // Index 3 -> BC_ISGT
    4,  // Index 4 -> BC_ISEQV
    5,  // Index 5 -> BC_ISNEV
    6,  // Index 6 -> BC_ISEQS
    7,  // Index 7 -> BC_ISNES
    8,  // Index 8 -> BC_ISEQN
    9,  // Index 9 -> BC_ISNEN
    10, // Index 10 -> BC_ISEQP
    11, // Index 11 -> BC_ISNEP
    13, // Index 12 -> BC_ISFC (False and Copy)
    12, // Index 13 -> BC_ISTC (True and Copy)
    15, // Index 14 -> BC_ISF (False, no copy)
    14, // Index 15 -> BC_IST (True, no copy)
    16, // Index 16 -> BC_ISTYPE
    17, // Index 17 -> BC_ISNUM
    19, // Index 18 -> BC_NOT
    18, // Index 19 -> BC_MOV
    21, // Index 20 -> BC_LEN
    20, // Index 21 -> BC_UNM
    22, // Index 22 -> BC_ADDVN
    23, // Index 23 -> BC_SUBVN
    24, // Index 24 -> BC_MULVN
    25, // Index 25 -> BC_DIVVN
    26, // Index 26 -> BC_MODVN
    27, // Index 27 -> BC_ADDNV
    28, // Index 28 -> BC_SUBNV
    29, // Index 29 -> BC_MULNV
    30, // Index 30 -> BC_DIVNV
    31, // Index 31 -> BC_MODNV
    32, // Index 32 -> BC_ADDVV
    33, // Index 33 -> BC_SUBVV
    34, // Index 34 -> BC_MULVV
    35, // Index 35 -> BC_DIVVV
    36, // Index 36 -> BC_MODVV
    37, // Index 37 -> BC_POW
    38, // Index 38 -> BC_CAT
    44, // Index 39 -> BC_UGET
    45, // Index 40 -> BC_USETV
    46, // Index 41 -> BC_USETS
    47, // Index 42 -> BC_USETN
    48, // Index 43 -> BC_USETP
    49, // Index 44 -> BC_UCLO
    50, // Index 45 -> BC_FNEW
    39, // Index 46 -> BC_KSTR
    40, // Index 47 -> BC_KCDATA
    41, // Index 48 -> BC_KSHORT
    42, // Index 49 -> BC_KNUM
    43, // Index 50 -> BC_KPRI
    44, // Index 51 -> BC_KNIL
    51, // Index 52 -> BC_TNEW
    52, // Index 53 -> BC_TDUP
    53, // Index 54 -> BC_GGET
    54, // Index 55 -> BC_GSET
    55, // Index 56 -> BC_TGETV
    56, // Index 57 -> BC_TGETS
    57, // Index 58 -> BC_TGETB
    58, // Index 59 -> BC_TGETR
    59, // Index 60 -> BC_TSETV
    60, // Index 61 -> BC_TSETS
    61, // Index 62 -> BC_TSETB
    62, // Index 63 -> BC_TSETM
    63, // Index 64 -> BC_TSETR
    64, // Index 65 -> BC_CALLM
    65, // Index 66 -> BC_CALL
    66, // Index 67 -> BC_CALLMT
    67, // Index 68 -> BC_CALLT
    68, // Index 69 -> BC_ITERC
    69, // Index 70 -> BC_ITERN
    70, // Index 71 -> BC_VARG (注意：您的列表跳过了71，这里用70)
    72, // Index 72 -> BC_ISNEXT
    73, // Index 73 -> BC_RETM
    74, // Index 74 -> BC_RET
    75, // Index 75 -> BC_RET0
    76, // Index 76 -> BC_RET1
    77, // Index 77 -> BC_FORI
    81, // Index 78 -> BC_JFORL (注意：您的列表显示这是JFORL)
    78, // Index 79 -> BC_FORL
    79, // Index 80 -> BC_IFORL
    81, // Index 81 -> BC_JFORL (重复)
    82, // Index 82 -> BC_ITERL
    83, // Index 83 -> BC_IITERL
    84, // Index 84 -> BC_JITERL
    85, // Index 85 -> BC_LOOP
    86, // Index 86 -> BC_ILOOP
    87, // Index 87 -> BC_JLOOP
    88, // Index 88 -> BC_JMP
    89, // Index 89 -> BC_FUNCF
    90, // Index 90 -> BC_IFUNCF
    91, // Index 91 -> BC_JFUNCF
    92, // Index 92 -> BC_FUNCV
    93, // Index 93 -> BC_IFUNCV
    94, // Index 94 -> BC_JFUNCV
    95, // Index 95 -> BC_FUNCC
    96  // Index 96 -> BC_FUNCCW
};

enum BC_OP {
	BC_OP_ISLT, // if A<VAR> < D<VAR> then JMP
	BC_OP_ISGE, // if not (A<VAR> < D<VAR>) then JMP
	BC_OP_ISLE, // if A<VAR> <= D<VAR> then JMP
	BC_OP_ISGT, // if not (A<VAR> <= D<VAR>) then JMP
	BC_OP_ISEQV, // if A<VAR> == D<VAR> then JMP
	BC_OP_ISNEV, // if A<VAR> ~= D<VAR> then JMP
	BC_OP_ISEQS, // if A<VAR> == D<STR> then JMP
	BC_OP_ISNES, // if A<VAR> ~= D<STR> then JMP
	BC_OP_ISEQN, // if A<VAR> == D<NUM> then JMP
	BC_OP_ISNEN, // if A<VAR> ~= D<NUM> then JMP
	BC_OP_ISEQP, // if A<VAR> == D<PRI> then JMP
	BC_OP_ISNEP, // if A<VAR> ~= D<PRI> then JMP
	BC_OP_ISTC, // if D<VAR> then A<DST> = D and JMP
	BC_OP_ISFC, // if not D<VAR> then A<DST> = D and JMP
	BC_OP_IST, // if D<VAR> then JMP
	BC_OP_ISF, // if not D<VAR> then JMP
	BC_OP_ISTYPE, // unsupported
	BC_OP_ISNUM, // unsupported
	BC_OP_MOV, // A<DST> = D<VAR>
	BC_OP_NOT, // A<DST> = not D<VAR>
	BC_OP_UNM, // A<DST> = -D<VAR>
	BC_OP_LEN, // A<DST> = #D<VAR>
	BC_OP_ADDVN, // A<DST> = B<VAR> + C<NUM>
	BC_OP_SUBVN, // A<DST> = B<VAR> - C<NUM>
	BC_OP_MULVN, // A<DST> = B<VAR> * C<NUM>
	BC_OP_DIVVN, // A<DST> = B<VAR> / C<NUM>
	BC_OP_MODVN, // A<DST> = B<VAR> % C<NUM>
	BC_OP_ADDNV, // A<DST> = C<NUM> + B<VAR>
	BC_OP_SUBNV, // A<DST> = C<NUM> - B<VAR>
	BC_OP_MULNV, // A<DST> = C<NUM> * B<VAR>
	BC_OP_DIVNV, // A<DST> = C<NUM> / B<VAR>
	BC_OP_MODNV, // A<DST> = C<NUM> % B<VAR>
	BC_OP_ADDVV, // A<DST> = B<VAR> + C<VAR>
	BC_OP_SUBVV, // A<DST> = B<VAR> - C<VAR>
	BC_OP_MULVV, // A<DST> = B<VAR> * C<VAR>
	BC_OP_DIVVV, // A<DST> = B<VAR> / C<VAR>
	BC_OP_MODVV, // A<DST> = B<VAR> % C<VAR>
	BC_OP_POW, // A<DST> = B<VAR> ^ C<VAR>
	BC_OP_CAT, // A<DST> = B<RBASE> .. B++ -> C<RBASE>
	BC_OP_KSTR, // A<DST> = D<STR>
	BC_OP_KCDATA, // A<DST> = D<CDATA>
	BC_OP_KSHORT, // A<DST> = D<LITS>
	BC_OP_KNUM, // A<DST> = D<NUM>
	BC_OP_KPRI, // A<DST> = D<PRI>
	BC_OP_KNIL, // A<BASE>, A++ -> D<BASE> = nil
	BC_OP_UGET, // A<DST> = D<UV>
	BC_OP_USETV, // A<UV> = D<VAR>
	BC_OP_USETS, // A<UV> = D<STR>
	BC_OP_USETN, // A<UV> = D<NUM>
	BC_OP_USETP, // A<UV> = D<PRI>
	BC_OP_UCLO, // upvalue close for A<RBASE>, A++ -> framesize; goto D<JUMP>
	BC_OP_FNEW, // A<DST> = D<FUNC>
	BC_OP_TNEW, // A<DST> = {}
	BC_OP_TDUP, // A<DST> = D<TAB>
	BC_OP_GGET, // A<DST> = _G.D<STR>
	BC_OP_GSET, // _G.D<STR> = A<VAR>
	BC_OP_TGETV, // A<DST> = B<VAR>[C<VAR>]
	BC_OP_TGETS, // A<DST> = B<VAR>[C<STR>]
	BC_OP_TGETB, // A<DST> = B<VAR>[C<LIT>]
	BC_OP_TGETR, // unsupported
	BC_OP_TSETV, // B<VAR>[C<VAR>] = A<VAR>
	BC_OP_TSETS, // B<VAR>[C<STR>] = A<VAR>
	BC_OP_TSETB, // B<VAR>[C<LIT>] = A<VAR>
	BC_OP_TSETM, // A-1<BASE>[D&0xFFFFFFFF<NUM>] <- A (<- multres)
	BC_OP_TSETR, // unsupported
	BC_OP_CALLM, // if B<LIT> == 0 then A<BASE> (<- multres) <- A(A+FR2?2:1, A++ -> for C<LIT>, A++ (<- multres)) else A, A++ -> for B-1 = A(A+FR2?2:1, A++ -> for C, A++ (<- multres))
	BC_OP_CALL, // if B<LIT> == 0 then A<BASE> (<- multres) <- A(A+FR2?2:1, A++ -> for C-1<LIT>) else A, A++ -> for B-1 = A(A+FR2?2:1, A++ -> for C-1)
	BC_OP_CALLMT, // return A<BASE>(A+FR2?2:1, A++ -> for D<LIT>, A++ (<- multres))
	BC_OP_CALLT, // return A<BASE>(A+FR2?2:1, A++ -> for D-1<LIT>)
	BC_OP_ITERC, // for A<BASE>, A++ -> for B-1<LIT> in A-3, A-2, A-1 do
	BC_OP_ITERN, // for A<BASE>, A++ -> for B-1<LIT> in A-3, A-2, A-1 do
	BC_OP_VARG, // if B<LIT> == 0 then A<BASE> (<- multres) <- ... else A, A++ -> for B-1 = ...
	BC_OP_ISNEXT, // goto ITERN at D<JUMP>
	BC_OP_RETM, // return A<BASE>, A++ -> for D<LIT>, A++ (<- multres)
	BC_OP_RET, // return A<RBASE>, A++ -> for D-1<LIT>
	BC_OP_RET0, // return
	BC_OP_RET1, // return A<RBASE>
	BC_OP_FORI, // for A+3<BASE> = A, A+1, A+2 do; exit at D<JUMP>
	BC_OP_JFORI, // unsupported
	BC_OP_FORL, // end of numeric for loop; start at D<JUMP>
	BC_OP_IFORL, // unsupported
	BC_OP_JFORL, // unsupported
	BC_OP_ITERL, // end of generic for loop; start at D<JUMP>
	BC_OP_IITERL, // unsupported
	BC_OP_JITERL, // unsupported
	BC_OP_LOOP, // if D<JUMP> == 32767 then goto loop else while/repeat loop; exit at D
	BC_OP_ILOOP, // unsupported
	BC_OP_JLOOP, // unsupported
	BC_OP_JMP, // goto D<JUMP> or if true then JMP or goto ITERC at D
	BC_OP_FUNCF, // unsupported
	BC_OP_IFUNCF, // unsupported
	BC_OP_JFUNCF, // unsupported
	BC_OP_FUNCV, // unsupported
	BC_OP_IFUNCV, // unsupported
	BC_OP_JFUNCV, // unsupported
	BC_OP_FUNCC, // unsupported
	BC_OP_FUNCCW, // unsupported
	BC_OP_INVALID
};

struct Instruction {
	BC_OP type;
	uint8_t a = 0;
	uint8_t b = 0;
	uint8_t c = 0;
	uint16_t d = 0;
};

static BC_OP get_op_type(const uint8_t& byte, const uint8_t& version) {
	if (g_useShuffledMapping) {
		if (byte < 97) {
			// 使用魔改LuaJIT的混淆映射表
			return (BC_OP)SHUFFLED_MAPPING[byte];
		} else {
			// 超出映射表范围的指令，映射为无效指令
			std::cout << "[WARNING] Opcode " << (int)byte << " is out of mapping range, treating as INVALID" << std::endl;
			return BC_OP_INVALID;
		}
	} else {
		// 使用原有的标准映射逻辑
		return (BC_OP)(version == 1 && byte >= BC_OP_ISTYPE ?
		              (byte >= BC_OP_TGETR - 2 ?
		               (byte >= BC_OP_TSETR - 3 ? byte + 4 : byte + 3) :
		               byte + 2) : byte);
	}
}

static bool is_op_abc_format(const BC_OP& instruction) {
	switch (instruction) {
	case BC_OP_ADDVN:
	case BC_OP_SUBVN:
	case BC_OP_MULVN:
	case BC_OP_DIVVN:
	case BC_OP_MODVN:
	case BC_OP_ADDNV:
	case BC_OP_SUBNV:
	case BC_OP_MULNV:
	case BC_OP_DIVNV:
	case BC_OP_MODNV:
	case BC_OP_ADDVV:
	case BC_OP_SUBVV:
	case BC_OP_MULVV:
	case BC_OP_DIVVV:
	case BC_OP_MODVV:
	case BC_OP_POW:
	case BC_OP_CAT:
	case BC_OP_TGETV:
	case BC_OP_TGETS:
	case BC_OP_TGETB:
	case BC_OP_TGETR:
	case BC_OP_TSETV:
	case BC_OP_TSETS:
	case BC_OP_TSETB:
	case BC_OP_TSETR:
	case BC_OP_CALLM:
	case BC_OP_CALL:
	case BC_OP_ITERC:
	case BC_OP_ITERN:
	case BC_OP_VARG:
		return true;
	}

	return false;
}
